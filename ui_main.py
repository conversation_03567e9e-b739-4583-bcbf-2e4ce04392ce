# Form implementation generated from reading ui file 'D:\学习pthon\sock\main.ui'
#
# Created by: PyQt6 UI code generator 6.4.2
#
# WARNING: Any manual changes made to this file will be lost when pyuic6 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt6 import QtCore, QtGui, QtWidgets


class Ui_Dialog(object):
    def setupUi(self, Dialog):
        Dialog.setObjectName("Dialog")
        Dialog.resize(741, 262)
        self.treeWidget = QtWidgets.QTreeWidget(parent=Dialog)
        self.treeWidget.setGeometry(QtCore.QRect(0, 0, 741, 211))
        self.treeWidget.setVerticalScrollBarPolicy(QtCore.Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        self.treeWidget.setObjectName("treeWidget")
        self.treeWidget.headerItem().setTextAlignment(0, QtCore.Qt.AlignmentFlag.AlignCenter)
        self.treeWidget.headerItem().setTextAlignment(1, QtCore.Qt.AlignmentFlag.AlignCenter)
        self.treeWidget.headerItem().setTextAlignment(2, QtCore.Qt.AlignmentFlag.AlignCenter)
        self.treeWidget.headerItem().setTextAlignment(3, QtCore.Qt.AlignmentFlag.AlignCenter)
        self.treeWidget.headerItem().setTextAlignment(4, QtCore.Qt.AlignmentFlag.AlignCenter)
        self.treeWidget.headerItem().setTextAlignment(5, QtCore.Qt.AlignmentFlag.AlignCenter)
        self.treeWidget.headerItem().setTextAlignment(6, QtCore.Qt.AlignmentFlag.AlignCenter)
        self.lineEdit = QtWidgets.QLineEdit(parent=Dialog)
        self.lineEdit.setGeometry(QtCore.QRect(60, 220, 41, 21))
        self.lineEdit.setObjectName("lineEdit")
        self.lowlatency = QtWidgets.QLineEdit(parent=Dialog)
        self.lowlatency.setGeometry(QtCore.QRect(110, 220, 41, 21))
        self.lowlatency.setObjectName("lowlatency")
        self.label = QtWidgets.QLabel(parent=Dialog)
        self.label.setGeometry(QtCore.QRect(0, 220, 61, 21))
        font = QtGui.QFont()
        font.setPointSize(10)
        self.label.setFont(font)
        self.label.setObjectName("label")
        self.Startall = QtWidgets.QPushButton(parent=Dialog)
        self.Startall.setGeometry(QtCore.QRect(370, 220, 61, 24))
        self.Startall.setObjectName("Startall")
        self.Stopall = QtWidgets.QPushButton(parent=Dialog)
        self.Stopall.setGeometry(QtCore.QRect(440, 220, 61, 24))
        self.Stopall.setObjectName("Stopall")
        self.Closeall = QtWidgets.QPushButton(parent=Dialog)
        self.Closeall.setGeometry(QtCore.QRect(260, 220, 101, 24))
        self.Closeall.setObjectName("Closeall")
        self.Launchbrowserall = QtWidgets.QPushButton(parent=Dialog)
        self.Launchbrowserall.setGeometry(QtCore.QRect(160, 220, 101, 24))
        self.Launchbrowserall.setObjectName("Launchbrowserall")
        self.Pause = QtWidgets.QPushButton(parent=Dialog)
        self.Pause.setGeometry(QtCore.QRect(630, 220, 51, 24))
        self.Pause.setObjectName("Pause")
        self.stop = QtWidgets.QPushButton(parent=Dialog)
        self.stop.setGeometry(QtCore.QRect(690, 220, 51, 24))
        self.stop.setObjectName("stop")
        self.Start = QtWidgets.QPushButton(parent=Dialog)
        self.Start.setGeometry(QtCore.QRect(570, 220, 51, 24))
        self.Start.setObjectName("Start")
        self.Launchbrowser = QtWidgets.QPushButton(parent=Dialog)
        self.Launchbrowser.setGeometry(QtCore.QRect(510, 220, 51, 24))
        self.Launchbrowser.setObjectName("Launchbrowser")

        self.retranslateUi(Dialog)
        QtCore.QMetaObject.connectSlotsByName(Dialog)

    def retranslateUi(self, Dialog):
        _translate = QtCore.QCoreApplication.translate
        Dialog.setWindowTitle(_translate("Dialog", "Dialog"))
        self.treeWidget.headerItem().setText(0, _translate("Dialog", "名称"))
        self.treeWidget.headerItem().setText(1, _translate("Dialog", "买入价格"))
        self.treeWidget.headerItem().setText(2, _translate("Dialog", "买入数量"))
        self.treeWidget.headerItem().setText(3, _translate("Dialog", "状态"))
        self.treeWidget.headerItem().setText(4, _translate("Dialog", "运行次数"))
        self.treeWidget.headerItem().setText(5, _translate("Dialog", "设置运行次数"))
        self.treeWidget.headerItem().setText(6, _translate("Dialog", "开关"))
        self.lineEdit.setText(_translate("Dialog", "0.5"))
        self.lowlatency.setText(_translate("Dialog", "2"))
        self.label.setText(_translate("Dialog", "随机延迟："))
        self.Startall.setText(_translate("Dialog", "全部开始"))
        self.Stopall.setText(_translate("Dialog", "全部停止"))
        self.Closeall.setText(_translate("Dialog", "全部关闭浏览器"))
        self.Launchbrowserall.setText(_translate("Dialog", "启动全部浏览器"))
        self.Pause.setText(_translate("Dialog", "暂停"))
        self.stop.setText(_translate("Dialog", "停止"))
        self.Start.setText(_translate("Dialog", "开始"))
        self.Launchbrowser.setText(_translate("Dialog", "启动"))
