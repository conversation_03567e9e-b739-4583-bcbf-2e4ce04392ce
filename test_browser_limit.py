"""
测试浏览器数量限制功能
"""
from browser_automation import BrowserAutomation
import time

def test_browser_limit():
    """测试浏览器数量限制功能"""
    print("=" * 60)
    print("测试浏览器数量限制功能")
    print("=" * 60)
    
    # 创建自动化实例
    automation = BrowserAutomation()
    
    # 1. 显示当前设置
    count_info = automation.get_browser_count_info()
    print(f"当前设置:")
    print(f"  最大浏览器数量: {count_info['max']}")
    print(f"  当前运行数量: {count_info['current']}")
    print(f"  剩余可启动: {count_info['remaining']}")
    print(f"  限制是否启用: {'是' if count_info['limit_enabled'] else '否'}")
    
    # 2. 测试设置最大数量
    print(f"\n2. 测试设置最大浏览器数量为 3")
    automation.set_max_browsers(3)
    
    count_info = automation.get_browser_count_info()
    print(f"  设置后最大数量: {count_info['max']}")
    
    # 3. 获取可用浏览器列表
    result = automation.get_browser_list(page=0)
    if not result.get("success") or not result["data"]["list"]:
        print("❌ 没有找到可用的浏览器窗口")
        return
    
    browsers = result["data"]["list"]
    print(f"\n3. 找到 {len(browsers)} 个未启动的浏览器")
    
    # 4. 测试启动浏览器直到达到限制
    print(f"\n4. 测试启动浏览器（最大限制: {automation.max_browsers}）")
    
    target_url = "https://www.binance.com/zh-CN/markets/alpha-initials"
    started_browsers = []
    
    for i, browser in enumerate(browsers):
        browser_id = browser['id']
        browser_name = browser['name']
        
        print(f"\n尝试启动第 {i+1} 个浏览器: {browser_name}")
        
        # 检查是否可以启动
        can_start, message = automation.can_start_more_browsers()
        print(f"  检查结果: {message}")
        
        if can_start:
            result = automation.open_browser(browser_id, target_url)
            if result.get("success"):
                started_browsers.append(browser_id)
                count_info = automation.get_browser_count_info()
                print(f"  ✅ 启动成功! 当前数量: {count_info['status_text']}")
            else:
                print(f"  ❌ 启动失败: {result.get('message', '未知错误')}")
        else:
            print(f"  🚫 无法启动: {message}")
            break
        
        time.sleep(1)  # 避免启动过快
    
    # 5. 显示最终状态
    final_count_info = automation.get_browser_count_info()
    print(f"\n5. 最终状态:")
    print(f"  成功启动: {len(started_browsers)} 个浏览器")
    print(f"  当前运行: {final_count_info['current']}")
    print(f"  最大限制: {final_count_info['max']}")
    print(f"  剩余可启动: {final_count_info['remaining']}")
    
    # 6. 测试超出限制的情况
    print(f"\n6. 测试超出限制的情况")
    if len(browsers) > automation.max_browsers:
        extra_browser = browsers[automation.max_browsers]
        browser_id = extra_browser['id']
        browser_name = extra_browser['name']
        
        print(f"尝试启动额外的浏览器: {browser_name}")
        can_start, message = automation.can_start_more_browsers()
        print(f"  检查结果: {message}")
        
        if not can_start:
            print(f"  🚫 正确阻止了超出限制的启动")
        else:
            print(f"  ⚠️  意外：应该被阻止但没有被阻止")
    
    # 7. 清理：关闭所有启动的浏览器
    print(f"\n7. 清理：关闭所有启动的浏览器")
    for browser_id in started_browsers:
        automation.close_browser(browser_id)
        print(f"  关闭浏览器: {browser_id[:8]}...")
    
    final_count_info = automation.get_browser_count_info()
    print(f"\n清理完成，当前运行数量: {final_count_info['current']}")

def test_dynamic_limit_change():
    """测试动态修改限制数量"""
    print("\n" + "=" * 60)
    print("测试动态修改限制数量")
    print("=" * 60)
    
    automation = BrowserAutomation()
    
    # 测试不同的限制数量
    test_limits = [1, 3, 5, 10]
    
    for limit in test_limits:
        print(f"\n设置最大浏览器数量为: {limit}")
        automation.set_max_browsers(limit)
        
        count_info = automation.get_browser_count_info()
        can_start, message = automation.can_start_more_browsers()
        
        print(f"  当前设置: {count_info['max']}")
        print(f"  当前状态: {message}")
        print(f"  可以启动: {'是' if can_start else '否'}")

def main():
    """主函数"""
    try:
        print("🚀 开始测试浏览器数量限制功能...")
        
        # 测试浏览器数量限制
        test_browser_limit()
        
        # 测试动态修改限制
        test_dynamic_limit_change()
        
        print("\n" + "=" * 60)
        print("🎉 浏览器数量限制功能测试完成!")
        print("=" * 60)
        print("✅ 功能说明:")
        print("   1. 可以设置最大浏览器启动数量")
        print("   2. 启动前会自动检查数量限制")
        print("   3. 超出限制时会阻止启动并提示用户")
        print("   4. 窗口标题会显示当前浏览器数量")
        print("   5. 支持动态修改最大数量限制")
        
        print("\n💡 使用方法:")
        print("   - 在界面中可以设置最大启动数量")
        print("   - 启动浏览器时会自动检查限制")
        print("   - 窗口标题显示 '当前数量/最大数量'")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
