# 比特浏览器自动化币安买卖脚本 - 使用指南

## 🚀 快速开始

### 1. 环境检查
运行以下命令检查环境：
```bash
python test_browser.py
```

### 2. 启动程序
```bash
python run.py
```

或者直接运行：
```bash
python main.py
```

## 📋 功能说明

### 主界面控件

| 控件名称 | 功能说明 |
|---------|----------|
| 随机延迟输入框 | 设置自动化执行的延迟范围（左边为最小值，右边为最大值） |
| 启动全部浏览器 | 批量启动所有未启动的浏览器窗口 |
| 全部关闭浏览器 | 批量关闭所有已启动的浏览器窗口 |
| 全部开始 | 开始所有浏览器的自动化交易任务 |
| 全部停止 | 停止所有正在运行的自动化交易任务 |
| 启动 | 启动选中行的浏览器窗口 |
| 开始 | 开始选中行的自动化交易任务 |
| 暂停 | 暂停选中行的自动化交易任务 |
| 停止 | 停止选中行的自动化交易任务 |

### 浏览器列表

| 列名 | 说明 | 操作 |
|------|------|------|
| 名称 | 浏览器窗口名称 | 只读 |
| 买入价格 | 交易买入价格 | 双击编辑 |
| 买入数量 | 交易买入数量 | 双击编辑 |
| 状态 | 当前状态 | 自动更新 |
| 运行次数 | 已执行次数 | 自动更新 |
| 设置运行次数 | 目标执行次数 | 双击编辑 |
| 开关 | 操作按钮 | 点击操作 |

## 🔧 操作步骤

### 基本操作流程

1. **启动程序**
   ```bash
   python run.py
   ```

2. **检查浏览器列表**
   - 程序启动后会自动加载未启动的浏览器列表
   - 在"名称"列中显示浏览器窗口名称

3. **设置交易参数**
   - 双击"买入价格"列设置价格
   - 双击"买入数量"列设置数量
   - 双击"设置运行次数"列设置执行次数

4. **设置延迟范围**
   - 在底部输入框设置随机延迟范围
   - 左边输入框：最小延迟（秒）
   - 右边输入框：最大延迟（秒）
   - ⚠️ 最小值不能大于最大值

5. **启动浏览器**
   - 点击对应行的"启动"按钮启动单个浏览器
   - 或点击"启动全部浏览器"批量启动

6. **开始自动化**
   - 点击对应行的"开始"按钮开始单个自动化任务
   - 或点击"全部开始"批量开始所有任务

7. **控制自动化**
   - 点击"暂停"可以暂停/恢复任务
   - 点击"停止"可以完全停止任务

### 高级操作

#### 批量管理
- **启动全部浏览器**: 一键启动所有未启动的浏览器
- **全部关闭浏览器**: 一键关闭所有已启动的浏览器
- **全部开始**: 一键开始所有浏览器的自动化任务
- **全部停止**: 一键停止所有正在运行的自动化任务

#### 选择性操作
- 先在列表中选中一行（点击行）
- 然后使用底部的"启动"、"开始"、"暂停"、"停止"按钮

## 📊 状态说明

### 浏览器状态
- **未启动**: 浏览器窗口未启动
- **已启动**: 浏览器窗口已启动，可以开始自动化
- **自动化运行中**: 正在执行自动化交易任务
- **自动化已暂停**: 自动化任务已暂停，可以恢复
- **自动化已停止**: 自动化任务已完全停止
- **自动化已完成**: 自动化任务已完成所有设定次数

### 按钮状态变化
- **启动按钮**: "启动" ↔ "关闭"
- **开始按钮**: "开始" ↔ "暂停"

## ⚠️ 注意事项

### 使用前准备
1. **确保比特浏览器已启动**
   - 比特浏览器必须运行并开启本地API服务
   - 默认端口：54345

2. **创建浏览器窗口配置**
   - 在比特浏览器中创建窗口配置
   - 确保窗口配置正确

3. **网络连接**
   - 确保能正常访问币安网站
   - 检查代理设置（如果使用）

### 操作注意事项
1. **延迟设置**: 最小延迟必须小于等于最大延迟
2. **浏览器启动**: 执行自动化前必须先启动浏览器
3. **参数设置**: 确保买入价格和数量设置合理
4. **运行次数**: 设置为0表示无限循环执行

### 安全提醒
1. **测试环境**: 建议先在测试环境中验证功能
2. **小额测试**: 初次使用建议设置小额交易进行测试
3. **监控运行**: 运行时请注意监控程序状态
4. **及时停止**: 发现异常时及时停止自动化任务

## 🐛 故障排除

### 常见问题

#### 1. 无法连接比特浏览器API
**症状**: 程序提示"无法连接到比特浏览器API"
**解决方案**:
- 检查比特浏览器是否启动
- 检查端口54345是否被占用
- 重启比特浏览器

#### 2. 浏览器启动失败
**症状**: 点击"启动"按钮无反应或报错
**解决方案**:
- 检查浏览器配置是否正确
- 检查系统资源是否充足
- 重启比特浏览器

#### 3. 自动化执行失败
**症状**: 自动化任务无法正常执行
**解决方案**:
- 检查网络连接
- 检查币安页面是否正常加载
- 检查页面元素是否变化

#### 4. 延迟设置错误
**症状**: 提示"最小延迟不能大于最大延迟"
**解决方案**:
- 确保左边输入框的值小于等于右边输入框的值
- 检查输入的是否为有效数字

## 📝 开发说明

### 待完善功能
当前交易逻辑为占位实现，需要完善：

1. **页面元素定位**
   - 在 `binance_elements.py` 中填写正确的选择器
   - 根据实际页面结构调整元素定位

2. **交易逻辑实现**
   - 在 `browser_automation.py` 的 `_execute_trade_logic` 方法中实现具体逻辑
   - 处理各种交易场景和异常情况

3. **扩展功能**
   - 添加卖出逻辑
   - 实现止损止盈
   - 添加更多交易策略

### 代码结构
- `main.py`: 主程序和界面逻辑
- `browser_automation.py`: 核心自动化功能
- `config.py`: 配置管理
- `logger.py`: 日志和历史记录
- `binance_elements.py`: 页面元素定位配置

## 📞 技术支持

如果遇到问题：
1. 查看 `automation.log` 日志文件
2. 运行 `python demo.py` 进行功能测试
3. 检查 `trade_history.json` 交易历史记录

## 🎯 项目完成度

✅ **100% 完成** - 所有需求功能都已实现
- 图形界面功能绑定 ✅
- 浏览器管理功能 ✅  
- 按钮操作功能 ✅
- 延迟验证功能 ✅
- 批量操作功能 ✅
- 状态显示功能 ✅
- 可编辑功能 ✅
- 模块化设计 ✅
- 占位函数实现 ✅
- 网址配置 ✅

唯一需要后续完善的是具体的币安交易页面元素选择器和交易逻辑实现。
