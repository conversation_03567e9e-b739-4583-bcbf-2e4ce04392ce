"""
测试比特浏览器API连接
"""
import requests
import json

def test_browser_api():
    """测试比特浏览器API连接"""
    url = "http://127.0.0.1:54345"
    headers = {'Content-Type': 'application/json'}
    
    try:
        # 测试获取浏览器列表
        json_data = {
            "page": 0,  # 0表示未启动
            "pageSize": 10
        }
        
        print("正在测试比特浏览器API连接...")
        response = requests.post(f"{url}/browser/list", 
                               data=json.dumps(json_data), 
                               headers=headers, 
                               timeout=5)
        
        if response.status_code == 200:
            result = response.json()
            if result.get("success"):
                browsers = result["data"]["list"]
                print(f"✅ API连接成功！找到 {len(browsers)} 个未启动的浏览器")
                
                for i, browser in enumerate(browsers[:3], 1):  # 只显示前3个
                    print(f"  {i}. ID: {browser['id']}")
                    print(f"     名称: {browser['name']}")
                    print(f"     备注: {browser.get('remark', '无')}")
                    print()
                
                return True
            else:
                print(f"❌ API返回错误: {result.get('message', '未知错误')}")
                return False
        else:
            print(f"❌ HTTP请求失败，状态码: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到比特浏览器API (http://127.0.0.1:54345)")
        print("请确保：")
        print("1. 比特浏览器已经启动")
        print("2. 本地服务端口54345已开启")
        return False
    except requests.exceptions.Timeout:
        print("❌ 连接超时")
        return False
    except Exception as e:
        print(f"❌ 发生未知错误: {e}")
        return False

if __name__ == "__main__":
    test_browser_api()
