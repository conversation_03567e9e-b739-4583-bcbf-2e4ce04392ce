"""
启动脚本 - 用于启动比特浏览器自动化程序
"""
import sys
import os

def check_dependencies():
    """检查依赖包"""
    required_packages = ['PyQt6', 'requests', 'selenium']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("❌ 缺少以下依赖包:")
        for package in missing_packages:
            print(f"   - {package}")
        print("\n请运行以下命令安装:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    print("✅ 所有依赖包已安装")
    return True

def check_browser_api():
    """检查比特浏览器API"""
    try:
        import requests
        import json
        
        url = "http://127.0.0.1:54345"
        headers = {'Content-Type': 'application/json'}
        json_data = {"page": 0, "pageSize": 1}
        
        response = requests.post(f"{url}/browser/list", 
                               data=json.dumps(json_data), 
                               headers=headers, 
                               timeout=3)
        
        if response.status_code == 200:
            result = response.json()
            if result.get("success"):
                print("✅ 比特浏览器API连接正常")
                return True
        
        print("❌ 比特浏览器API连接失败")
        return False
        
    except Exception as e:
        print(f"❌ 比特浏览器API连接失败: {e}")
        print("请确保比特浏览器已启动并开启本地API服务")
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("比特浏览器自动化币安买卖脚本")
    print("=" * 50)
    
    # 检查依赖
    if not check_dependencies():
        input("按回车键退出...")
        return
    
    # 检查API
    if not check_browser_api():
        print("\n⚠️  警告: API连接失败，程序可能无法正常工作")
        choice = input("是否继续启动程序? (y/n): ")
        if choice.lower() != 'y':
            return
    
    print("\n🚀 启动主程序...")
    
    try:
        # 导入并启动主程序
        from main import MainWindow
        from PyQt6.QtWidgets import QApplication
        
        app = QApplication(sys.argv)
        window = MainWindow()
        window.show()
        
        print("✅ 程序启动成功!")
        print("📝 使用说明:")
        print("   1. 双击'买入价格'、'买入数量'、'设置运行次数'列可以编辑")
        print("   2. 设置随机延迟范围（最小值不能大于最大值）")
        print("   3. 先启动浏览器，再开始自动化任务")
        print("   4. 可以随时暂停/恢复自动化任务")
        
        sys.exit(app.exec())
        
    except Exception as e:
        print(f"❌ 程序启动失败: {e}")
        input("按回车键退出...")

if __name__ == "__main__":
    main()
