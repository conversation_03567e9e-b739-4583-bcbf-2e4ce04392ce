"""
比特浏览器自动化功能模块
处理浏览器启动、关闭、自动化交易等功能
"""
import requests
import json
import time
import threading
import random
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from PyQt6.QtCore import QObject, pyqtSignal, QTimer
from PyQt6.QtWidgets import QMessageBox
from config import get_config
from logger import (log_info, log_error, log_warning, log_browser_action,
                   log_automation_action, log_trade_action, log_error_with_traceback,
                   save_trade_record)
from binance_elements import (BINANCE_URLS, find_element_with_multiple_selectors,
                             wait_for_page_load, ACTION_CONFIG)


class BrowserAutomation(QObject):
    """比特浏览器自动化类"""
    
    # 信号定义
    status_updated = pyqtSignal(str, str)  # browser_id, status
    def __init__(self):
        super().__init__()
        # 从配置文件加载设置
        api_config = get_config('browser_api')
        self.url = api_config['url']
        self.headers = api_config['headers']
        self.timeout = api_config['timeout']

        self.running_browsers = {}  # 存储正在运行的浏览器信息
        self.automation_threads = {}  # 存储自动化线程
        self.automation_status = {}  # 存储自动化状态 {'browser_id': 'running'/'paused'/'stopped'}
        
    def get_browser_list(self, page=0):
        """获取浏览器列表
        Args:
            page: 0表示未启动, 1表示启动
        Returns:
            dict: API响应结果
        """
        json_data = {
            "page": page,
            "pageSize": 100
        }
        try:
            res = requests.post(f"{self.url}/browser/list",
                              data=json.dumps(json_data), headers=self.headers,
                              timeout=self.timeout).json()
            log_info(f"获取浏览器列表成功，页面: {page}")
            return res
        except Exception as e:
            log_error_with_traceback("获取浏览器列表失败", e)
            return {"success": False, "message": str(e)}
    
    def open_browser(self, browser_id):
        """启动浏览器
        Args:
            browser_id: 浏览器ID
        Returns:
            dict: API响应结果
        """
        json_data = {"id": browser_id}
        try:
            res = requests.post(f"{self.url}/browser/open",
                              data=json.dumps(json_data), headers=self.headers,
                              timeout=self.timeout).json()
            if res.get("success"):
                self.running_browsers[browser_id] = res['data']
                self.status_updated.emit(browser_id, "已启动")
                log_browser_action(browser_id, "启动", "success")
            else:
                log_browser_action(browser_id, "启动", "error", res.get('message', '未知错误'))
            return res
        except Exception as e:
            log_error_with_traceback(f"启动浏览器失败: {browser_id}", e)
            return {"success": False, "message": str(e)}
    
    def close_browser(self, browser_id):
        """关闭浏览器
        Args:
            browser_id: 浏览器ID
        """
        json_data = {'id': browser_id}
        try:
            # 先停止自动化
            self.stop_automation(browser_id)

            # 关闭浏览器
            requests.post(f"{self.url}/browser/close",
                         data=json.dumps(json_data), headers=self.headers).json()
            if browser_id in self.running_browsers:
                del self.running_browsers[browser_id]
            self.status_updated.emit(browser_id, "未启动")
        except Exception as e:
            print(f"关闭浏览器失败: {e}")
    
    def validate_delay_settings(self, min_delay, max_delay):
        """验证延迟设置
        Args:
            min_delay: 最小延迟
            max_delay: 最大延迟
        Returns:
            bool: 验证是否通过
        """
        try:
            min_val = float(min_delay)
            max_val = float(max_delay)
            if min_val > max_val:
                return False, "最小延迟不能大于最大延迟"
            if min_val < 0 or max_val < 0:
                return False, "延迟时间不能为负数"
            return True, ""
        except ValueError:
            return False, "延迟时间必须为数字"
    
    def get_random_delay(self, min_delay, max_delay):
        """获取随机延迟时间
        Args:
            min_delay: 最小延迟
            max_delay: 最大延迟
        Returns:
            float: 随机延迟时间
        """
        try:
            min_val = float(min_delay)
            max_val = float(max_delay)
            return random.uniform(min_val, max_val)
        except ValueError:
            return 1.0  # 默认延迟1秒
    
    def start_automation(self, browser_id, buy_price, buy_quantity, run_count, min_delay, max_delay):
        """开始自动化交易
        Args:
            browser_id: 浏览器ID
            buy_price: 买入价格
            buy_quantity: 买入数量
            run_count: 运行次数
            min_delay: 最小延迟
            max_delay: 最大延迟
        """
        # 验证延迟设置
        is_valid, error_msg = self.validate_delay_settings(min_delay, max_delay)
        if not is_valid:
            QMessageBox.warning(None, "延迟设置错误", error_msg)
            return False
        
        # 检查浏览器是否已启动
        if browser_id not in self.running_browsers:
            QMessageBox.warning(None, "浏览器未启动", "请先启动浏览器")
            return False
        
        # 停止之前的自动化任务
        self.stop_automation(browser_id)
        
        # 设置自动化状态
        self.automation_status[browser_id] = 'running'
        self.status_updated.emit(browser_id, "自动化运行中")
        
        # 创建并启动自动化线程
        thread = threading.Thread(
            target=self._automation_worker,
            args=(browser_id, buy_price, buy_quantity, run_count, min_delay, max_delay),
            daemon=True
        )
        self.automation_threads[browser_id] = thread
        thread.start()
        
        return True
    
    def pause_automation(self, browser_id):
        """暂停自动化交易"""
        if browser_id in self.automation_status:
            if self.automation_status[browser_id] == 'running':
                self.automation_status[browser_id] = 'paused'
                self.status_updated.emit(browser_id, "自动化已暂停")
                return True
            elif self.automation_status[browser_id] == 'paused':
                self.automation_status[browser_id] = 'running'
                self.status_updated.emit(browser_id, "自动化运行中")
                return True
        return False
    
    def stop_automation(self, browser_id):
        """停止自动化交易"""
        if browser_id in self.automation_status:
            self.automation_status[browser_id] = 'stopped'
            self.status_updated.emit(browser_id, "自动化已停止")
            
            # 清理线程
            if browser_id in self.automation_threads:
                del self.automation_threads[browser_id]
    
    def _automation_worker(self, browser_id, buy_price, buy_quantity, run_count, min_delay, max_delay):
        """自动化工作线程"""
        try:
            # 获取浏览器连接信息
            browser_data = self.running_browsers.get(browser_id)
            if not browser_data:
                return
            
            # 创建Selenium连接
            driver = self._create_selenium_driver(browser_data)
            if not driver:
                return
            
            # 导航到币安页面
            binance_url = BINANCE_URLS['alpha_initials']
            driver.get(binance_url)
            log_info(f"导航到币安页面: {binance_url}")

            # 等待页面加载
            if not wait_for_page_load(driver):
                log_error(f"页面加载超时: {browser_id}")
                return
            
            executed_count = 0
            target_count = int(run_count) if run_count else float('inf')
            print(f"开始自动化交易 - 价格: {buy_price}, 数量: {buy_quantity}, 运行次数: {target_count}")
            
            while (executed_count < target_count and 
                   self.automation_status.get(browser_id) != 'stopped'):
                
                # 检查是否暂停
                while self.automation_status.get(browser_id) == 'paused':
                    time.sleep(0.5)
                    if self.automation_status.get(browser_id) == 'stopped':
                        break
                
                if self.automation_status.get(browser_id) == 'stopped':
                    break
                
                # 执行买卖逻辑（占位函数）
                success = self._execute_trade_logic(driver, buy_price, buy_quantity)
                print(f"successd的值为：{success}")
                if success:
                    executed_count += 1
                    log_trade_action(browser_id, "交易执行", buy_price, buy_quantity, "成功")
                    print(f"执行交易次数: {executed_count}")


                    # 保存交易记录
                    trade_data = {
                        "action": "buy",
                        "price": buy_price,
                        "quantity": buy_quantity,
                        "result": "success",
                        "execution_count": executed_count
                    }
                    save_trade_record(browser_id, trade_data)
                else:
                    log_trade_action(browser_id, "交易执行", buy_price, buy_quantity, "失败")
                
                # 随机延迟
                delay = self.get_random_delay(min_delay, max_delay)
                time.sleep(delay)
            
            # 完成后更新状态
            if self.automation_status.get(browser_id) != 'stopped':
                self.automation_status[browser_id] = 'completed'
                self.status_updated.emit(browser_id, "自动化已完成")
            
            driver.quit()
            
        except Exception as e:
            print(f"自动化执行错误: {e}")
            self.status_updated.emit(browser_id, "自动化执行错误")
    
    def _create_selenium_driver(self, browser_data):
        """创建Selenium驱动"""
        try:
            driver_path = browser_data['driver']
            debugger_address = browser_data['http']
            
            chrome_options = webdriver.ChromeOptions()
            chrome_options.add_experimental_option("debuggerAddress", debugger_address)
            
            chrome_service = Service(driver_path)
            driver = webdriver.Chrome(service=chrome_service, options=chrome_options)
            
            return driver
        except Exception as e:
            print(f"创建Selenium驱动失败: {e}")
            return None
    
    def _execute_trade_logic(self, driver, buy_price, buy_quantity):
        """执行交易逻辑（占位函数）
        Args:
            driver: Selenium WebDriver
            buy_price: 买入价格
            buy_quantity: 买入数量
        Returns:
            bool: 执行是否成功
        """
        # TODO: 实现具体的币安交易逻辑
        try:
            log_trade_action(None, "开始执行交易逻辑", buy_price, buy_quantity)

            # 1. 查找价格输入框（占位）
            # price_element = find_element_with_multiple_selectors(driver, 'trading', 'price_input')
            # if price_element:
            #     price_element.clear()
            #     price_element.send_keys(str(buy_price))
            #     time.sleep(ACTION_CONFIG['input_delay'])

            # 2. 查找数量输入框（占位）
            # quantity_element = find_element_with_multiple_selectors(driver, 'trading', 'quantity_input')
            # if quantity_element:
            #     quantity_element.clear()
            #     quantity_element.send_keys(str(buy_quantity))
            #     time.sleep(ACTION_CONFIG['input_delay'])

            # 3. 点击买入按钮（占位）
            # buy_button = find_element_with_multiple_selectors(driver, 'trading', 'buy_button')
            # if buy_button:
            #     buy_button.click()
            #     time.sleep(ACTION_CONFIG['click_delay'])

            # 4. 确认交易（占位）
            # confirm_button = find_element_with_multiple_selectors(driver, 'trading', 'confirm_button')
            # if confirm_button:
            #     confirm_button.click()
            #     time.sleep(ACTION_CONFIG['click_delay'])

            # 占位代码 - 模拟交易成功
            time.sleep(1)  # 模拟操作时间
            log_info(f"模拟交易完成 - 价格: {buy_price}, 数量: {buy_quantity}")

            return True

        except Exception as e:
            log_error_with_traceback("交易执行失败", e)
            return False
    
    def get_browser_status(self, browser_id):
        """获取浏览器状态"""
        if browser_id in self.running_browsers:
            if browser_id in self.automation_status:
                auto_status = self.automation_status[browser_id]
                if auto_status == 'running':
                    return "自动化运行中"
                elif auto_status == 'paused':
                    return "自动化已暂停"
                elif auto_status == 'stopped':
                    return "已启动"
                elif auto_status == 'completed':
                    return "自动化已完成"
            return "已启动"
        else:
            return "未启动"
    
    def is_automation_running(self, browser_id):
        """检查自动化是否正在运行"""
        return (browser_id in self.automation_status and 
                self.automation_status[browser_id] in ['running', 'paused'])
    
    def is_browser_running(self, browser_id):
        """检查浏览器是否正在运行"""
        return browser_id in self.running_browsers

    def get_all_browser_status(self):
        """获取所有浏览器的状态信息"""
        # 获取未启动的浏览器
        not_running_result = self.get_browser_list(page=0)
        # 获取已启动的浏览器
        running_result = self.get_browser_list(page=1)

        status_info = {}

        # 处理未启动的浏览器
        if not_running_result.get("success"):
            for browser in not_running_result["data"]["list"]:
                browser_id = browser['id']
                status_info[browser_id] = {
                    'name': browser['name'],
                    'browser_status': '未启动',
                    'automation_status': '未运行'
                }

        # 处理已启动的浏览器
        if running_result.get("success"):
            for browser in running_result["data"]["list"]:
                browser_id = browser['id']
                auto_status = self.automation_status.get(browser_id, 'stopped')
                automation_text = {
                    'running': '运行中',
                    'paused': '已暂停',
                    'stopped': '未运行',
                    'completed': '已完成'
                }.get(auto_status, '未运行')

                status_info[browser_id] = {
                    'name': browser['name'],
                    'browser_status': '已启动',
                    'automation_status': automation_text
                }

        return status_info


class BinanceTrader:
    """币安交易类（占位）"""
    
    def __init__(self, driver):
        self.driver = driver
        self.binance_url = "https://www.binance.com/zh-CN/markets/alpha-initials"
    
    def navigate_to_binance(self):
        """导航到币安页面"""
        try:
            self.driver.get(self.binance_url)
            # 等待页面加载
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            return True
        except Exception as e:
            print(f"导航到币安页面失败: {e}")
            return False
    
    def execute_buy_order(self, price, quantity):
        """执行买入订单（占位函数）"""
        # TODO: 实现具体的买入逻辑
        # 需要根据实际页面元素来实现
        print(f"执行买入订单 - 价格: {price}, 数量: {quantity}")
        
        # 占位逻辑
        try:
            # 1. 查找价格输入框
            # price_input = self.driver.find_element(By.XPATH, "//input[@placeholder='价格']")
            # price_input.clear()
            # price_input.send_keys(str(price))
            
            # 2. 查找数量输入框
            # quantity_input = self.driver.find_element(By.XPATH, "//input[@placeholder='数量']")
            # quantity_input.clear()
            # quantity_input.send_keys(str(quantity))
            
            # 3. 点击买入按钮
            # buy_button = self.driver.find_element(By.XPATH, "//button[contains(text(), '买入')]")
            # buy_button.click()
            
            # 4. 确认交易
            # confirm_button = self.driver.find_element(By.XPATH, "//button[contains(text(), '确认')]")
            # confirm_button.click()
            
            time.sleep(1)  # 模拟操作时间
            return True
            
        except Exception as e:
            print(f"执行买入订单失败: {e}")
            return False
    
    def execute_sell_order(self, price, quantity):
        """执行卖出订单（占位函数）"""
        # TODO: 实现具体的卖出逻辑
        print(f"执行卖出订单 - 价格: {price}, 数量: {quantity}")
        
        # 占位逻辑
        try:
            time.sleep(1)  # 模拟操作时间
            return True
        except Exception as e:
            print(f"执行卖出订单失败: {e}")
            return False


def show_error_message(title, message):
    """显示错误消息"""
    msg_box = QMessageBox()
    msg_box.setIcon(QMessageBox.Icon.Warning)
    msg_box.setWindowTitle(title)
    msg_box.setText(message)
    msg_box.exec()


def show_info_message(title, message):
    """显示信息消息"""
    msg_box = QMessageBox()
    msg_box.setIcon(QMessageBox.Icon.Information)
    msg_box.setWindowTitle(title)
    msg_box.setText(message)
    msg_box.exec()
