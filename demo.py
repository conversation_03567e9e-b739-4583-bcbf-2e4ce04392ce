"""
演示脚本 - 展示主要功能
"""
from browser_automation import BrowserAutomation
from logger import log_info, log_browser_action
import time

def demo_browser_automation():
    """演示浏览器自动化功能"""
    print("=" * 60)
    print("比特浏览器自动化功能演示")
    print("=" * 60)
    
    # 创建自动化实例
    automation = BrowserAutomation()
    
    # 1. 获取浏览器列表
    print("\n1. 获取未启动的浏览器列表...")
    result = automation.get_browser_list(page=0)
    
    if result.get("success"):
        browsers = result["data"]["list"]
        print(f"✅ 找到 {len(browsers)} 个未启动的浏览器:")
        
        for i, browser in enumerate(browsers, 1):
            print(f"   {i}. ID: {browser['id'][:8]}...")
            print(f"      名称: {browser['name']}")
            print(f"      备注: {browser.get('remark', '无')}")
            print()
        
        if browsers:
            # 选择第一个浏览器进行演示
            demo_browser = browsers[0]
            browser_id = demo_browser['id']
            browser_name = demo_browser['name']
            
            print(f"2. 演示浏览器操作 - 使用浏览器: {browser_name}")
            
            # 2. 测试延迟验证
            print("\n3. 测试延迟验证功能...")
            
            # 正确的延迟设置
            is_valid, msg = automation.validate_delay_settings("0.5", "2.0")
            print(f"   延迟设置 0.5-2.0: {'✅ 有效' if is_valid else '❌ 无效'} {msg}")
            
            # 错误的延迟设置
            is_valid, msg = automation.validate_delay_settings("2.0", "0.5")
            print(f"   延迟设置 2.0-0.5: {'✅ 有效' if is_valid else '❌ 无效'} {msg}")
            
            # 3. 测试随机延迟生成
            print("\n4. 测试随机延迟生成...")
            for i in range(3):
                delay = automation.get_random_delay("0.5", "2.0")
                print(f"   随机延迟 {i+1}: {delay:.2f}秒")
            
            # 4. 测试状态获取
            print("\n5. 测试状态获取...")
            status = automation.get_browser_status(browser_id)
            print(f"   浏览器状态: {status}")
            
            is_running = automation.is_browser_running(browser_id)
            print(f"   是否运行中: {'是' if is_running else '否'}")
            
            is_auto_running = automation.is_automation_running(browser_id)
            print(f"   自动化是否运行中: {'是' if is_auto_running else '否'}")
            
            print("\n6. 功能演示完成!")
            print("   💡 提示: 运行 'python run.py' 启动完整的图形界面程序")
            
        else:
            print("❌ 没有找到可用的浏览器窗口")
            print("   请在比特浏览器中创建一些窗口配置")
    
    else:
        print(f"❌ 获取浏览器列表失败: {result.get('message', '未知错误')}")
        print("   请确保比特浏览器已启动并开启本地API服务")

def demo_config_system():
    """演示配置系统"""
    print("\n" + "=" * 60)
    print("配置系统演示")
    print("=" * 60)
    
    from config import get_config, validate_config
    
    # 显示主要配置
    api_config = get_config('browser_api')
    trade_config = get_config('default_trade')
    ui_config = get_config('ui')
    
    print(f"API地址: {api_config['url']}")
    print(f"默认买入价格: {trade_config['buy_price']}")
    print(f"默认买入数量: {trade_config['buy_quantity']}")
    print(f"延迟范围: {trade_config['min_delay']}-{trade_config['max_delay']}秒")
    print(f"状态刷新间隔: {ui_config['status_refresh_interval']}毫秒")
    
    # 验证配置
    print("\n配置验证结果:")
    errors = validate_config()
    if errors:
        for error in errors:
            print(f"❌ {error}")
    else:
        print("✅ 所有配置验证通过")

def demo_logging_system():
    """演示日志系统"""
    print("\n" + "=" * 60)
    print("日志系统演示")
    print("=" * 60)
    
    from logger import (log_info, log_warning, log_error, 
                       log_browser_action, log_automation_action, 
                       log_trade_action, save_trade_record, get_trade_history)
    
    # 演示各种日志
    log_info("这是一条信息日志")
    log_warning("这是一条警告日志")
    log_browser_action("demo_browser", "启动", "success")
    log_automation_action("demo_browser", "开始自动化", "价格: 0.001, 数量: 100")
    log_trade_action("demo_browser", "买入", "0.001", "100", "成功")
    
    # 演示交易历史记录
    trade_data = {
        "action": "buy",
        "price": "0.001",
        "quantity": "100",
        "result": "success"
    }
    save_trade_record("demo_browser", trade_data)
    
    history = get_trade_history("demo_browser")
    print(f"✅ 交易历史记录数量: {len(history)}")

def main():
    """主演示函数"""
    try:
        # 演示浏览器自动化功能
        demo_browser_automation()
        
        # 演示配置系统
        demo_config_system()
        
        # 演示日志系统
        demo_logging_system()
        
        print("\n" + "=" * 60)
        print("演示完成!")
        print("=" * 60)
        print("🚀 运行 'python run.py' 启动完整程序")
        print("📖 查看 'README.md' 了解详细使用说明")
        print("📋 查看 '项目总结.md' 了解项目完成情况")
        
    except Exception as e:
        print(f"❌ 演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
