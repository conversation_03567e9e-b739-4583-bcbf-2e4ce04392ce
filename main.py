import requests
import json
import time

url = "http://127.0.0.1:54345"
headers = {'Content-Type': 'application/json'}

def listBrowsers():
    json_data = {
        "page": 0, # 0表示未启动  1表示启动
        "pageSize": 100  # 可根据需要调整
    }
    res = requests.post(f"{url}/browser/list",
                        data=json.dumps(json_data), headers=headers).json()
    return res

def openBrowser(id):
    json_data = {"id": f"{id}"}
    res = requests.post(f"{url}/browser/open",
                        data=json.dumps(json_data), headers=headers).json()
    return res


def countNotRunningBrowsers():
    result = listBrowsers()
    if result.get("success"):
        browsers = result["data"]["list"]
        not_running = [b for b in browsers if b.get("status") == 0]
        print(f"✅ 未运行的窗口数量: {len(not_running)}")
        for i, b in enumerate(not_running, 1):
            print(f"{i}. {b['id']} - {b['name']} - 备注: {b.get('remark','')}")
            res = openBrowser(b["id"])
            if res.get("success"):
                print(f"🚀 已启动: {b['id']} - {b['name']}")
                time.sleep(2)  # 每次启动间隔2秒，避免系统卡顿
            else:
                print(f"❌ 启动失败: {b['id']} - {res}")

    else:
        print("❌ 获取窗口列表失败:", result)



if __name__ == '__main__':
    countNotRunningBrowsers()
