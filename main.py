"""
比特浏览器自动化币安买卖脚本主程序
"""
import sys
import time
from PyQt6.QtWidgets import (QApplication, QDialog, QTreeWidgetItem, QPushButton,
                            QMessageBox, QLineEdit, QWidget, QHBoxLayout)
from PyQt6.QtCore import Qt, pyqtSlot, QTimer
from ui_main import Ui_Dialog
from browser_automation import BrowserAutomation, show_error_message, show_info_message
from config import get_config
from PyQt6.QtWidgets import QStyledItemDelegate


class ReadOnlyDelegate(QStyledItemDelegate):
    def __init__(self, editable_columns, parent=None):
        super().__init__(parent)
        self.editable_columns = editable_columns

    def createEditor(self, parent, option, index):
        if index.column() in self.editable_columns:
            return super().createEditor(parent, option, index)
        return None  # 不可编辑

class MainWindow(QDialog):
    def __init__(self):
        super().__init__()
        self.ui = Ui_Dialog()
        self.ui.setupUi(self)

        # 初始化浏览器自动化
        self.browser_automation = BrowserAutomation()
        self.browser_automation.status_updated.connect(self.update_browser_status)
        self.browser_automation.run_count_updated.connect(self.update_run_count)

        # 存储浏览器数据
        self.browser_data = {}

        # 绑定按钮事件
        self.setup_connections()

        # 加载浏览器列表
        self.load_browser_list()

        # 设置TreeWidget可编辑
        self.setup_tree_widget()

        # 设置定时器刷新状态
        ui_config = get_config('ui')
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self.refresh_status)
        self.status_timer.start(ui_config['status_refresh_interval'])

        # 设置窗口标题
        self.setWindowTitle(ui_config['window_title'])

        # 设置默认延迟值
        trade_config = get_config('default_trade')
        self.ui.lineEdit.setText(trade_config['min_delay'])
        self.ui.lowlatency.setText(trade_config['max_delay'])



    def setup_connections(self):
        """设置按钮连接"""
        self.ui.Launchbrowserall.clicked.connect(self.launch_all_browsers)
        self.ui.Closeall.clicked.connect(self.close_all_browsers)
        self.ui.Startall.clicked.connect(self.start_all_automation)
        self.ui.Stopall.clicked.connect(self.stop_all_automation)
        self.ui.Launchbrowser.clicked.connect(self.launch_selected_browser)
        self.ui.Start.clicked.connect(self.start_selected_automation)
        self.ui.Pause.clicked.connect(self.pause_selected_automation)
        self.ui.stop.clicked.connect(self.stop_selected_automation)

    def setup_tree_widget(self):
        """设置TreeWidget"""
        # 从配置文件获取列宽设置
        ui_config = get_config('ui')
        column_widths = ui_config['column_widths']

        # 设置列宽
        self.ui.treeWidget.setColumnWidth(0, column_widths['name'])
        self.ui.treeWidget.setColumnWidth(1, column_widths['buy_price'])
        self.ui.treeWidget.setColumnWidth(2, column_widths['buy_quantity'])
        self.ui.treeWidget.setColumnWidth(3, column_widths['status'])
        self.ui.treeWidget.setColumnWidth(4, column_widths['run_count'])
        self.ui.treeWidget.setColumnWidth(5, column_widths['set_run_count'])
        self.ui.treeWidget.setColumnWidth(6, column_widths['actions'])


        # 连接双击编辑事件
        #self.ui.treeWidget.itemDoubleClicked.connect(self.on_item_double_clicked)

    def load_browser_list(self):
        """加载浏览器列表"""
        result = self.browser_automation.get_browser_list(page=0)  # 获取未启动的浏览器

        if result.get("success"):
            browsers = result["data"]["list"]
            self.ui.treeWidget.clear()

            for browser in browsers:
                browser_id = browser['id']
                self.browser_data[browser_id] = browser

                # 从配置文件获取默认值
                trade_config = get_config('default_trade')

                # 创建树形项
                item = QTreeWidgetItem()
                item.setText(0, browser['name'])  # 名称
                item.setText(1, trade_config['buy_price'])  # 默认买入价格
                item.setText(2, trade_config['buy_quantity'])    # 默认买入数量
                item.setText(3, "未启动")  # 状态
                item.setText(4, "0")      # 运行次数
                item.setText(5, trade_config['run_count'])      # 设置运行次数
                for col in range(item.columnCount()):
                    item.setTextAlignment(col, Qt.AlignmentFlag.AlignCenter)
                #设置 125可编辑
                for col in [1, 2, 5]:
                    item.setFlags(item.flags() | Qt.ItemFlag.ItemIsEditable)

                # 存储浏览器ID
                item.setData(0, Qt.ItemDataRole.UserRole, browser_id)

                # 添加到树形控件
                self.ui.treeWidget.addTopLevelItem(item)

                # 创建开关列的按钮
                self.create_action_buttons(item, browser_id)
            delegate = ReadOnlyDelegate(editable_columns=[1, 2, 5])
            self.ui.treeWidget.setItemDelegate(delegate)
            from PyQt6.QtWidgets import QAbstractItemView
            self.ui.treeWidget.setEditTriggers(QAbstractItemView.EditTrigger.DoubleClicked)
        else:
            show_error_message("错误", f"获取浏览器列表失败: {result.get('message', '未知错误')}")

    def create_action_buttons(self, item, browser_id):
        """为每行创建启动和开始按钮"""
        button_widget = QWidget()
        layout = QHBoxLayout(button_widget)
        layout.setContentsMargins(2, 2, 2, 2)

        # 启动按钮
        launch_btn = QPushButton("启动")
        launch_btn.setFixedSize(50, 25)
        launch_btn.clicked.connect(lambda: self.launch_browser(browser_id))

        # 开始/暂停按钮
        start_btn = QPushButton("开始")
        start_btn.setFixedSize(50, 25)
        start_btn.clicked.connect(lambda: self.toggle_automation(browser_id, start_btn))

        layout.addWidget(launch_btn)
        layout.addWidget(start_btn)

        # 设置到树形控件
        self.ui.treeWidget.setItemWidget(item, 6, button_widget)

        # 存储按钮引用
        item.setData(6, Qt.ItemDataRole.UserRole, {'launch': launch_btn, 'start': start_btn})

    def refresh_status(self):
        """刷新所有浏览器状态"""
        for i in range(self.ui.treeWidget.topLevelItemCount()):
            item = self.ui.treeWidget.topLevelItem(i)
            browser_id = item.data(0, Qt.ItemDataRole.UserRole)

            # 更新状态
            status = self.browser_automation.get_browser_status(browser_id)
            item.setText(3, status)

            # 更新按钮状态
            buttons = item.data(6, Qt.ItemDataRole.UserRole)
            if buttons:
                # 更新启动按钮
                if self.browser_automation.is_browser_running(browser_id):
                    buttons['launch'].setText("关闭")
                    buttons['launch'].clicked.disconnect()
                    buttons['launch'].clicked.connect(lambda checked, bid=browser_id: self.close_browser(bid))
                else:
                    buttons['launch'].setText("启动")
                    buttons['launch'].clicked.disconnect()
                    buttons['launch'].clicked.connect(lambda checked, bid=browser_id: self.launch_browser(bid))

                # 更新开始/暂停按钮
                if self.browser_automation.is_automation_running(browser_id):
                    auto_status = self.browser_automation.automation_status.get(browser_id)
                    if auto_status == 'running':
                        buttons['start'].setText("暂停")
                    elif auto_status == 'paused':
                        buttons['start'].setText("开始")
                else:
                    buttons['start'].setText("开始")

    def close_browser(self, browser_id):
        """关闭指定浏览器"""
        self.browser_automation.close_browser(browser_id)
        show_info_message("成功", "浏览器已关闭")

    def launch_browser(self, browser_id):
        """启动指定浏览器"""
        # 从界面获取目标网址
        target_url = self.ui.lineEdit_2.text().strip() if hasattr(self.ui, 'lineEdit_2') else ""

        # 如果没有网址，使用默认网址
        if not target_url:
            target_url = "https://www.binance.com/zh-CN/markets/alpha-initials"

        result = self.browser_automation.open_browser(browser_id, target_url)
        print("浏览器启动后的result", result)
        if result.get("success"):
            #show_info_message("成功", f"浏览器启动成功并导航到:\n{target_url}")
            show_info_message("成功", "浏览器启动成功")
        else:
            show_error_message("错误", f"浏览器启动失败: {result.get('message', '未知错误')}")

    def toggle_automation(self, browser_id, button):
        """切换自动化状态"""
        if not self.browser_automation.is_browser_running(browser_id):
            show_error_message("错误", "请先启动浏览器")
            return

        current_text = button.text()
        if current_text == "开始":
            # 获取交易参数
            item = self.find_item_by_browser_id(browser_id)
            if item:
                buy_price = item.text(1)
                buy_quantity = item.text(2)
                run_count = item.text(5)
                min_delay = self.ui.lineEdit.text()
                max_delay = self.ui.lowlatency.text()

                # 开始自动化
                if self.browser_automation.start_automation(browser_id, buy_price, buy_quantity,
                                                          run_count, min_delay, max_delay):
                    button.setText("暂停")
        else:  # 暂停
            if self.browser_automation.pause_automation(browser_id):
                if self.browser_automation.automation_status.get(browser_id) == 'running':
                    button.setText("暂停")
                else:
                    button.setText("开始")

    def find_item_by_browser_id(self, browser_id):
        """根据浏览器ID查找树形项"""
        for i in range(self.ui.treeWidget.topLevelItemCount()):
            item = self.ui.treeWidget.topLevelItem(i)
            if item.data(0, Qt.ItemDataRole.UserRole) == browser_id:
                return item
        return None

    def update_browser_status(self, browser_id, status):
        """更新浏览器状态"""
        item = self.find_item_by_browser_id(browser_id)
        if item:
            item.setText(3, status)

    def update_run_count(self, browser_id, count):
        """更新运行次数"""
        item = self.find_item_by_browser_id(browser_id)
        if item:
            item.setText(4, str(count))

    def launch_all_browsers(self):
        """启动所有浏览器"""
        # 从界面获取目标网址
        target_url = self.ui.lineEdit_2.text().strip() if hasattr(self.ui, 'lineEdit_2') else ""

        # 如果没有网址，使用默认网址
        if not target_url:
            target_url = "https://www.binance.com/zh-CN/markets/alpha-initials"

        result = self.browser_automation.get_browser_list(page=0)
        if result.get("success"):
            browsers = result["data"]["list"]
            success_count = 0
            for browser in browsers:
                browser_id = browser['id']
                if not self.browser_automation.is_browser_running(browser_id):
                    res = self.browser_automation.open_browser(browser_id, target_url)
                    if res.get("success"):
                        success_count += 1
                    time.sleep(1)  # 避免启动过快
            #show_info_message("完成", f"成功启动 {success_count} 个浏览器\n导航到: {target_url}")
            show_info_message("完成", f"成功启动 {success_count} 个浏览器")

    def close_all_browsers(self):
        """关闭所有浏览器"""
        closed_count = 0
        for browser_id in list(self.browser_automation.running_browsers.keys()):
            self.browser_automation.close_browser(browser_id)
            closed_count += 1
        show_info_message("完成", f"成功关闭 {closed_count} 个浏览器")

    def start_all_automation(self):
        """开始所有自动化"""
        started_count = 0
        min_delay = self.ui.lineEdit.text()
        max_delay = self.ui.lowlatency.text()

        # 验证延迟设置
        is_valid, error_msg = self.browser_automation.validate_delay_settings(min_delay, max_delay)
        if not is_valid:
            show_error_message("延迟设置错误", error_msg)
            return

        for i in range(self.ui.treeWidget.topLevelItemCount()):
            item = self.ui.treeWidget.topLevelItem(i)
            browser_id = item.data(0, Qt.ItemDataRole.UserRole)

            if (self.browser_automation.is_browser_running(browser_id) and
                not self.browser_automation.is_automation_running(browser_id)):

                buy_price = item.text(1)
                buy_quantity = item.text(2)
                run_count = item.text(5)

                if self.browser_automation.start_automation(browser_id, buy_price, buy_quantity,
                                                          run_count, min_delay, max_delay):
                    started_count += 1
                    # 更新按钮状态
                    buttons = item.data(6, Qt.ItemDataRole.UserRole)
                    if buttons:
                        buttons['start'].setText("暂停")

        show_info_message("完成", f"成功开始 {started_count} 个自动化任务")

    def stop_all_automation(self):
        """停止所有自动化"""
        stopped_count = 0
        for i in range(self.ui.treeWidget.topLevelItemCount()):
            item = self.ui.treeWidget.topLevelItem(i)
            browser_id = item.data(0, Qt.ItemDataRole.UserRole)

            if self.browser_automation.is_automation_running(browser_id):
                self.browser_automation.stop_automation(browser_id)
                stopped_count += 1
                # 更新按钮状态
                buttons = item.data(6, Qt.ItemDataRole.UserRole)
                if buttons:
                    buttons['start'].setText("开始")

        show_info_message("完成", f"成功停止 {stopped_count} 个自动化任务")

    def launch_selected_browser(self):
        """启动选中的浏览器"""
        current_item = self.ui.treeWidget.currentItem()
        if current_item:
            browser_id = current_item.data(0, Qt.ItemDataRole.UserRole)
            self.launch_browser(browser_id)
        else:
            show_error_message("错误", "请先选择一个浏览器")

    def start_selected_automation(self):
        """开始选中的自动化"""
        current_item = self.ui.treeWidget.currentItem()
        if current_item:
            browser_id = current_item.data(0, Qt.ItemDataRole.UserRole)
            buttons = current_item.data(6, Qt.ItemDataRole.UserRole)
            if buttons:
                self.toggle_automation(browser_id, buttons['start'])
        else:
            show_error_message("错误", "请先选择一个浏览器")

    def pause_selected_automation(self):
        """暂停选中的自动化"""
        current_item = self.ui.treeWidget.currentItem()
        if current_item:
            browser_id = current_item.data(0, Qt.ItemDataRole.UserRole)
            if self.browser_automation.is_automation_running(browser_id):
                self.browser_automation.pause_automation(browser_id)
                buttons = current_item.data(6, Qt.ItemDataRole.UserRole)
                if buttons:
                    if self.browser_automation.automation_status.get(browser_id) == 'running':
                        buttons['start'].setText("暂停")
                    else:
                        buttons['start'].setText("开始")
        else:
            show_error_message("错误", "请先选择一个浏览器")

    def stop_selected_automation(self):
        """停止选中的自动化"""
        current_item = self.ui.treeWidget.currentItem()
        if current_item:
            browser_id = current_item.data(0, Qt.ItemDataRole.UserRole)
            self.browser_automation.stop_automation(browser_id)
            buttons = current_item.data(6, Qt.ItemDataRole.UserRole)
            if buttons:
                buttons['start'].setText("开始")
        else:
            show_error_message("错误", "请先选择一个浏览器")

    def on_item_double_clicked(self, item, column):
        """处理双击编辑事件"""
        # 只允许编辑买入价格(1)、买入数量(2)、设置运行次数(5)列

        if column in [1, 2, 5]:
            print("你双击了:", item.text(column))
            #self.ui.treeWidget.editItem(item, column)
            #item.setFlags(item.flags() | Qt.ItemFlag.ItemIsEditable)



if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec())
