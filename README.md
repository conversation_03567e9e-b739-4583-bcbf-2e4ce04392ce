# 比特浏览器自动化币安买卖脚本

这是一个基于PyQt6的比特浏览器自动化币安买卖脚本，支持批量管理多个浏览器窗口并执行自动化交易。

## 功能特性

- ✅ 图形化界面管理多个比特浏览器窗口
- ✅ 批量启动/关闭浏览器
- ✅ 自动化交易脚本（占位实现，待完善）
- ✅ 可编辑的交易参数（价格、数量、运行次数）
- ✅ 随机延迟设置
- ✅ 实时状态监控
- ✅ 暂停/恢复功能

## 文件结构

```
├── main.py                 # 主程序入口
├── ui_main.py             # PyQt6界面文件
├── browser_automation.py  # 浏览器自动化功能模块
├── bit_api.py             # 比特浏览器API示例
├── bit_playwright.py      # Playwright自动化示例
├── bit_selenium.py        # Selenium自动化示例
├── test_browser.py        # API连接测试脚本
└── README.md              # 说明文档
```

## 安装依赖

```bash
pip install PyQt6 requests selenium playwright
```

## 使用方法

### 1. 启动比特浏览器
确保比特浏览器已启动并开启本地API服务（默认端口54345）

### 2. 测试API连接
```bash
python test_browser.py
```

### 3. 运行主程序
```bash
python main.py
```

## 界面说明

### 主界面控件

- **随机延迟设置**: 设置自动化执行的随机延迟范围
- **启动全部浏览器**: 批量启动所有未启动的浏览器
- **全部关闭浏览器**: 批量关闭所有已启动的浏览器
- **全部开始**: 开始所有浏览器的自动化任务
- **全部停止**: 停止所有正在运行的自动化任务
- **启动**: 启动选中的浏览器
- **开始**: 开始选中浏览器的自动化任务
- **暂停**: 暂停选中浏览器的自动化任务
- **停止**: 停止选中浏览器的自动化任务

### 浏览器列表

| 列名 | 说明 | 可编辑 |
|------|------|--------|
| 名称 | 浏览器窗口名称 | ❌ |
| 买入价格 | 交易买入价格 | ✅ (双击编辑) |
| 买入数量 | 交易买入数量 | ✅ (双击编辑) |
| 状态 | 浏览器和自动化状态 | ❌ |
| 运行次数 | 已执行次数 | ❌ |
| 设置运行次数 | 目标执行次数 | ✅ (双击编辑) |
| 开关 | 启动和开始按钮 | ❌ |

## 状态说明

### 浏览器状态
- **未启动**: 浏览器窗口未启动
- **已启动**: 浏览器窗口已启动
- **自动化运行中**: 正在执行自动化交易
- **自动化已暂停**: 自动化任务已暂停
- **自动化已停止**: 自动化任务已停止
- **自动化已完成**: 自动化任务已完成

## 注意事项

1. **延迟设置**: 最小延迟不能大于最大延迟
2. **浏览器启动**: 执行自动化前必须先启动对应的浏览器
3. **API连接**: 确保比特浏览器的本地API服务正常运行
4. **交易逻辑**: 当前交易逻辑为占位实现，需要根据实际需求完善

## 开发说明

### 自动化交易逻辑

当前在 `browser_automation.py` 的 `_execute_trade_logic` 方法中为占位实现。
需要根据币安页面的实际元素来完善：

```python
def _execute_trade_logic(self, driver, buy_price, buy_quantity):
    """执行交易逻辑"""
    # TODO: 实现具体的币安交易逻辑
    # 1. 查找交易对
    # 2. 输入买入价格
    # 3. 输入买入数量  
    # 4. 点击买入按钮
    # 5. 确认交易
    pass
```

### 页面元素定位

目标网址: https://www.binance.com/zh-CN/markets/alpha-initials

需要找到以下元素的选择器：
- 价格输入框
- 数量输入框
- 买入按钮
- 确认按钮

### 扩展功能

可以考虑添加的功能：
- 卖出逻辑
- 止损止盈
- 交易记录
- 数据统计
- 配置文件保存

## 故障排除

### 常见问题

1. **无法连接API**
   - 检查比特浏览器是否启动
   - 检查端口54345是否开启

2. **浏览器启动失败**
   - 检查浏览器配置是否正确
   - 检查系统资源是否充足

3. **自动化执行失败**
   - 检查网络连接
   - 检查页面元素是否变化
   - 检查延迟设置是否合理

## 免责声明

本项目仅供学习和研究使用，使用者需要自行承担使用风险。
自动化交易存在风险，请谨慎使用。
