写一个特比浏览器自动化币安买卖脚本
1.图形界面我已经设置好，需要绑定功能。
2.读取所有比特浏览器的窗口（未启动窗口）其中把“b['name']”显示到界面的超级列表框的“名称”列中。
3.每一行的开关列中加多两个按钮“启动”“开始”，其中“启动“按钮启动对应比特浏览器的窗口，”开始“执行自动买卖脚本。点击按钮开始后“开始”变成“暂停”，点击“暂停”能暂停自动买卖脚本。“暂停”按钮又变成“开始”。
4.每执行一次自动买卖脚本，从界面获取随机延迟大小两数其中lineEdit为小，lowlatency为大，这里要加一个检测，小的不能大于大的，否则弹框给用户一个提示。
5.“启动全部浏览器”按钮点击就启动所有还未启动的窗口，“全部关闭浏览器”按钮点击就关闭所有已经启动的窗口。“全部开始”按钮点击就开始全部还启动执行自动买卖脚本。“全部停止”按钮单击就停止正在运行的执行自动买卖脚本。“开始”按钮点击就选中超级列表框中一列开始执行自动买卖脚本。“暂停”按钮点击就选中超级列表框中一列暂停执行自动买卖脚本。“停止”按钮点击就选中超级列表框中一列停止执行自动买卖脚本。
6.超级列表框中的状态栏，要显示状态，比如浏览器已启动，未启动，是否执行自动买卖，还是暂停，要显示状态。
7.超级列表框的”买入价格“，”买入数量“，”设置运行次数“双击能修改他们的值。
8.执行自动币安买卖脚本逻辑先不写，用函数先占位，后续再加上去。
9.其中main.py为主函数（里面的代码我是用来测试怎么检测未启用窗口，跟如何启动比特浏览器的，你可以直接删除修改掉。），ui_main.py为图形，功能函数还未添加，你看要不要新建一个文件专门写功能函数（我希望专门一个文件来写功能函数，方便理解）。
10.项目下的bit_api.py，bit_playwright.py，bit_selenium.py三个文件未比特浏览器的demo文件，你可以查看下怎么来实现自动化。
11.需要自动化的网址是：https://www.binance.com/zh-CN/markets/alpha-initials，可以把连接那些都写好，最后执行脚本的空出来，因为我还没找到对应的xp元素或者css选择器