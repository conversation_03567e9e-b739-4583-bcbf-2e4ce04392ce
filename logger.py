"""
日志模块 - 处理程序日志记录
"""
import logging
import os
from datetime import datetime
from logging.handlers import RotatingFileHandler
from config import get_config


class AutomationLogger:
    """自动化日志记录器"""
    
    def __init__(self, name="automation"):
        self.logger = logging.getLogger(name)
        self.setup_logger()
    
    def setup_logger(self):
        """设置日志记录器"""
        log_config = get_config('log')
        
        if not log_config['enable_logging']:
            return
        
        # 设置日志级别
        level = getattr(logging, log_config['log_level'].upper(), logging.INFO)
        self.logger.setLevel(level)
        
        # 避免重复添加处理器
        if self.logger.handlers:
            return
        
        # 创建格式器
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        # 控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(level)
        console_handler.setFormatter(formatter)
        self.logger.addHandler(console_handler)
        
        # 文件处理器
        if log_config['log_file']:
            file_handler = RotatingFileHandler(
                log_config['log_file'],
                maxBytes=log_config['max_log_size'],
                backupCount=log_config['backup_count'],
                encoding='utf-8'
            )
            file_handler.setLevel(level)
            file_handler.setFormatter(formatter)
            self.logger.addHandler(file_handler)
    
    def info(self, message):
        """记录信息日志"""
        self.logger.info(message)
    
    def warning(self, message):
        """记录警告日志"""
        self.logger.warning(message)
    
    def error(self, message):
        """记录错误日志"""
        self.logger.error(message)
    
    def debug(self, message):
        """记录调试日志"""
        self.logger.debug(message)
    
    def critical(self, message):
        """记录严重错误日志"""
        self.logger.critical(message)
    
    def log_browser_action(self, browser_id, action, status="success", message=""):
        """记录浏览器操作日志"""
        log_message = f"浏览器 {browser_id} - {action} - {status}"
        if message:
            log_message += f" - {message}"
        
        if status == "success":
            self.info(log_message)
        elif status == "warning":
            self.warning(log_message)
        else:
            self.error(log_message)
    
    def log_automation_action(self, browser_id, action, details=None):
        """记录自动化操作日志"""
        log_message = f"自动化 {browser_id} - {action}"
        if details:
            log_message += f" - {details}"
        self.info(log_message)
    
    def log_trade_action(self, browser_id, action, price=None, quantity=None, result=""):
        """记录交易操作日志"""
        log_message = f"交易 {browser_id} - {action}"
        if price and quantity:
            log_message += f" - 价格: {price}, 数量: {quantity}"
        if result:
            log_message += f" - 结果: {result}"
        self.info(log_message)
    
    def log_error_with_traceback(self, message, exception=None):
        """记录带堆栈跟踪的错误日志"""
        if exception:
            self.logger.exception(f"{message}: {str(exception)}")
        else:
            self.error(message)


# 全局日志实例
automation_logger = AutomationLogger()

# 便捷函数
def log_info(message):
    """记录信息日志"""
    automation_logger.info(message)

def log_warning(message):
    """记录警告日志"""
    automation_logger.warning(message)

def log_error(message):
    """记录错误日志"""
    automation_logger.error(message)

def log_debug(message):
    """记录调试日志"""
    automation_logger.debug(message)

def log_browser_action(browser_id, action, status="success", message=""):
    """记录浏览器操作日志"""
    automation_logger.log_browser_action(browser_id, action, status, message)

def log_automation_action(browser_id, action, details=None):
    """记录自动化操作日志"""
    automation_logger.log_automation_action(browser_id, action, details)

def log_trade_action(browser_id, action, price=None, quantity=None, result=""):
    """记录交易操作日志"""
    automation_logger.log_trade_action(browser_id, action, price, quantity, result)

def log_error_with_traceback(message, exception=None):
    """记录带堆栈跟踪的错误日志"""
    automation_logger.log_error_with_traceback(message, exception)


class TradeHistoryLogger:
    """交易历史记录器"""
    
    def __init__(self):
        self.data_config = get_config('data')
        self.history_file = self.data_config['history_file']
    
    def save_trade_record(self, browser_id, trade_data):
        """保存交易记录"""
        if not self.data_config['save_trade_history']:
            return
        
        try:
            import json
            
            # 添加时间戳
            trade_data['timestamp'] = datetime.now().isoformat()
            trade_data['browser_id'] = browser_id
            
            # 读取现有记录
            history = []
            if os.path.exists(self.history_file):
                with open(self.history_file, 'r', encoding='utf-8') as f:
                    history = json.load(f)
            
            # 添加新记录
            history.append(trade_data)
            
            # 保存到文件
            with open(self.history_file, 'w', encoding='utf-8') as f:
                json.dump(history, f, ensure_ascii=False, indent=2)
            
            log_info(f"交易记录已保存: {browser_id}")
            
        except Exception as e:
            log_error_with_traceback("保存交易记录失败", e)
    
    def get_trade_history(self, browser_id=None):
        """获取交易历史"""
        try:
            import json
            
            if not os.path.exists(self.history_file):
                return []
            
            with open(self.history_file, 'r', encoding='utf-8') as f:
                history = json.load(f)
            
            if browser_id:
                history = [record for record in history if record.get('browser_id') == browser_id]
            
            return history
            
        except Exception as e:
            log_error_with_traceback("读取交易历史失败", e)
            return []


# 全局交易历史记录器
trade_history_logger = TradeHistoryLogger()

def save_trade_record(browser_id, trade_data):
    """保存交易记录"""
    trade_history_logger.save_trade_record(browser_id, trade_data)

def get_trade_history(browser_id=None):
    """获取交易历史"""
    return trade_history_logger.get_trade_history(browser_id)


if __name__ == "__main__":
    # 测试日志功能
    print("测试日志功能...")
    
    log_info("程序启动")
    log_browser_action("test_browser", "启动", "success")
    log_automation_action("test_browser", "开始自动化", "价格: 0.001, 数量: 100")
    log_trade_action("test_browser", "买入", "0.001", "100", "成功")
    log_warning("这是一个警告")
    log_error("这是一个错误")
    
    # 测试交易历史
    trade_data = {
        "action": "buy",
        "price": "0.001",
        "quantity": "100",
        "result": "success"
    }
    save_trade_record("test_browser", trade_data)
    
    history = get_trade_history("test_browser")
    print(f"交易历史记录数量: {len(history)}")
    
    print("日志测试完成")
