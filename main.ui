<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>Dialog</class>
 <widget class="QDialog" name="Dialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>741</width>
    <height>262</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Dialog</string>
  </property>
  <widget class="QTreeWidget" name="treeWidget">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>741</width>
     <height>211</height>
    </rect>
   </property>
   <property name="verticalScrollBarPolicy">
    <enum>Qt::ScrollBarAsNeeded</enum>
   </property>
   <column>
    <property name="text">
     <string>名称</string>
    </property>
    <property name="textAlignment">
     <set>AlignCenter</set>
    </property>
   </column>
   <column>
    <property name="text">
     <string>买入价格</string>
    </property>
    <property name="textAlignment">
     <set>AlignCenter</set>
    </property>
   </column>
   <column>
    <property name="text">
     <string>买入数量</string>
    </property>
    <property name="textAlignment">
     <set>AlignCenter</set>
    </property>
   </column>
   <column>
    <property name="text">
     <string>状态</string>
    </property>
    <property name="textAlignment">
     <set>AlignCenter</set>
    </property>
   </column>
   <column>
    <property name="text">
     <string>运行次数</string>
    </property>
    <property name="textAlignment">
     <set>AlignCenter</set>
    </property>
   </column>
   <column>
    <property name="text">
     <string>设置运行次数</string>
    </property>
    <property name="textAlignment">
     <set>AlignCenter</set>
    </property>
   </column>
   <column>
    <property name="text">
     <string>开关</string>
    </property>
    <property name="textAlignment">
     <set>AlignCenter</set>
    </property>
   </column>
  </widget>
  <widget class="QLineEdit" name="lineEdit">
   <property name="geometry">
    <rect>
     <x>60</x>
     <y>220</y>
     <width>41</width>
     <height>21</height>
    </rect>
   </property>
   <property name="text">
    <string>0.5</string>
   </property>
  </widget>
  <widget class="QLineEdit" name="lowlatency">
   <property name="geometry">
    <rect>
     <x>110</x>
     <y>220</y>
     <width>41</width>
     <height>21</height>
    </rect>
   </property>
   <property name="text">
    <string>2</string>
   </property>
  </widget>
  <widget class="QLabel" name="label">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>220</y>
     <width>61</width>
     <height>21</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>10</pointsize>
    </font>
   </property>
   <property name="text">
    <string>随机延迟：</string>
   </property>
  </widget>
  <widget class="QPushButton" name="Startall">
   <property name="geometry">
    <rect>
     <x>370</x>
     <y>220</y>
     <width>61</width>
     <height>24</height>
    </rect>
   </property>
   <property name="text">
    <string>全部开始</string>
   </property>
  </widget>
  <widget class="QPushButton" name="Stopall">
   <property name="geometry">
    <rect>
     <x>440</x>
     <y>220</y>
     <width>61</width>
     <height>24</height>
    </rect>
   </property>
   <property name="text">
    <string>全部停止</string>
   </property>
  </widget>
  <widget class="QPushButton" name="Closeall">
   <property name="geometry">
    <rect>
     <x>260</x>
     <y>220</y>
     <width>101</width>
     <height>24</height>
    </rect>
   </property>
   <property name="text">
    <string>全部关闭浏览器</string>
   </property>
  </widget>
  <widget class="QPushButton" name="Launchbrowserall">
   <property name="geometry">
    <rect>
     <x>160</x>
     <y>220</y>
     <width>101</width>
     <height>24</height>
    </rect>
   </property>
   <property name="text">
    <string>启动全部浏览器</string>
   </property>
  </widget>
  <widget class="QPushButton" name="Pause">
   <property name="geometry">
    <rect>
     <x>630</x>
     <y>220</y>
     <width>51</width>
     <height>24</height>
    </rect>
   </property>
   <property name="text">
    <string>暂停</string>
   </property>
  </widget>
  <widget class="QPushButton" name="stop">
   <property name="geometry">
    <rect>
     <x>690</x>
     <y>220</y>
     <width>51</width>
     <height>24</height>
    </rect>
   </property>
   <property name="text">
    <string>停止</string>
   </property>
  </widget>
  <widget class="QPushButton" name="Start">
   <property name="geometry">
    <rect>
     <x>570</x>
     <y>220</y>
     <width>51</width>
     <height>24</height>
    </rect>
   </property>
   <property name="text">
    <string>开始</string>
   </property>
  </widget>
  <widget class="QPushButton" name="Launchbrowser">
   <property name="geometry">
    <rect>
     <x>510</x>
     <y>220</y>
     <width>51</width>
     <height>24</height>
    </rect>
   </property>
   <property name="text">
    <string>启动</string>
   </property>
  </widget>
 </widget>
 <resources/>
 <connections/>
</ui>
