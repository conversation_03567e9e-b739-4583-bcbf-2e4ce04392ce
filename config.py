"""
配置文件 - 存储程序的默认设置
"""

# 比特浏览器API配置
BROWSER_API_CONFIG = {
    'url': 'http://127.0.0.1:54345',
    'headers': {'Content-Type': 'application/json'},
    'timeout': 10  # 请求超时时间（秒）
}

# 默认交易参数
DEFAULT_TRADE_CONFIG = {
    'buy_price': '0.001',      # 默认买入价格
    'buy_quantity': '100',     # 默认买入数量
    'run_count': '10',          # 默认运行次数
    'min_delay': '0.5',        # 默认最小延迟（秒）
    'max_delay': '2.0'         # 默认最大延迟（秒）
}

# 币安交易配置
BINANCE_CONFIG = {
    'url': 'https://www.binance.com/zh-CN/markets/alpha-initials',
    'page_load_timeout': 30,   # 页面加载超时时间（秒）
    'element_wait_timeout': 10  # 元素等待超时时间（秒）
}

# 界面配置
UI_CONFIG = {
    'window_title': '比特浏览器自动化币安买卖脚本',
    'status_refresh_interval': 5000,  # 状态刷新间隔（毫秒）
    'column_widths': {
        'name': 150,           # 名称列宽度
        'buy_price': 80,       # 买入价格列宽度
        'buy_quantity': 80,    # 买入数量列宽度
        'status': 120,         # 状态列宽度
        'run_count': 80,       # 运行次数列宽度
        'set_run_count': 100,  # 设置运行次数列宽度
        'actions': 150         # 开关列宽度
    }
}

# 日志配置
LOG_CONFIG = {
    'enable_logging': False,
    'log_level': 'INFO',
    'log_file': 'automation.log',
    'max_log_size': 10 * 1024 * 1024,  # 10MB
    'backup_count': 5
}

# 安全配置
SECURITY_CONFIG = {
    'max_concurrent_browsers': 10,     # 最大并发浏览器数量
    'max_automation_threads': 20,     # 最大自动化线程数量
    'browser_startup_delay': 2,       # 浏览器启动间隔（秒）
    'automation_startup_delay': 1     # 自动化启动间隔（秒）
}

# 错误处理配置
ERROR_CONFIG = {
    'max_retry_count': 3,             # 最大重试次数
    'retry_delay': 5,                 # 重试延迟（秒）
    'auto_recovery': True,            # 是否自动恢复
    'error_notification': True        # 是否显示错误通知
}

# 交易策略配置（占位）
TRADE_STRATEGY_CONFIG = {
    'strategy_type': 'simple_buy',    # 策略类型
    'risk_management': {
        'max_loss_percent': 5,        # 最大亏损百分比
        'max_position_size': 1000,    # 最大仓位大小
        'stop_loss_enabled': False,   # 是否启用止损
        'take_profit_enabled': False  # 是否启用止盈
    }
}

# 数据存储配置
DATA_CONFIG = {
    'save_trade_history': False,       # 是否保存交易历史
    'history_file': 'trade_history.json',
    'save_browser_config': True,      # 是否保存浏览器配置
    'config_file': 'browser_config.json'
}

def get_config(config_name):
    """获取指定配置"""
    config_map = {
        'browser_api': BROWSER_API_CONFIG,
        'default_trade': DEFAULT_TRADE_CONFIG,
        'binance': BINANCE_CONFIG,
        'ui': UI_CONFIG,
        'log': LOG_CONFIG,
        'security': SECURITY_CONFIG,
        'error': ERROR_CONFIG,
        'trade_strategy': TRADE_STRATEGY_CONFIG,
        'data': DATA_CONFIG
    }
    return config_map.get(config_name, {})

def update_config(config_name, key, value):
    """更新配置值"""
    config_map = {
        'browser_api': BROWSER_API_CONFIG,
        'default_trade': DEFAULT_TRADE_CONFIG,
        'binance': BINANCE_CONFIG,
        'ui': UI_CONFIG,
        'log': LOG_CONFIG,
        'security': SECURITY_CONFIG,
        'error': ERROR_CONFIG,
        'trade_strategy': TRADE_STRATEGY_CONFIG,
        'data': DATA_CONFIG
    }
    
    config = config_map.get(config_name)
    if config and key in config:
        config[key] = value
        return True
    return False

# 验证配置的有效性
def validate_config():
    """验证配置的有效性"""
    errors = []
    
    # 验证延迟设置
    min_delay = float(DEFAULT_TRADE_CONFIG['min_delay'])
    max_delay = float(DEFAULT_TRADE_CONFIG['max_delay'])
    if min_delay > max_delay:
        errors.append("默认最小延迟不能大于最大延迟")
    
    # 验证数值范围
    if SECURITY_CONFIG['max_concurrent_browsers'] <= 0:
        errors.append("最大并发浏览器数量必须大于0")
    
    if UI_CONFIG['status_refresh_interval'] < 1000:
        errors.append("状态刷新间隔不能小于1秒")
    
    return errors

if __name__ == "__main__":
    # 测试配置
    print("配置验证结果:")
    errors = validate_config()
    if errors:
        for error in errors:
            print(f"❌ {error}")
    else:
        print("✅ 所有配置验证通过")
    
    print("\n当前配置:")
    print(f"API地址: {BROWSER_API_CONFIG['url']}")
    print(f"默认买入价格: {DEFAULT_TRADE_CONFIG['buy_price']}")
    print(f"默认买入数量: {DEFAULT_TRADE_CONFIG['buy_quantity']}")
    print(f"延迟范围: {DEFAULT_TRADE_CONFIG['min_delay']}-{DEFAULT_TRADE_CONFIG['max_delay']}秒")
