"""
配置文件 - 存储程序的默认设置

这个文件采用配置化设计模式，将所有可调整的参数集中管理。

🎯 设计目标：
1. 参数外置：避免在代码中硬编码参数值
2. 易于修改：修改配置不需要改代码
3. 分类管理：按功能模块分组管理配置
4. 类型安全：提供配置验证功能
5. 默认值：为所有参数提供合理的默认值

📋 配置分类：
- BROWSER_API_CONFIG: 比特浏览器API相关配置
- DEFAULT_TRADE_CONFIG: 默认交易参数配置
- BINANCE_CONFIG: 币安网站相关配置
- UI_CONFIG: 用户界面相关配置
- LOG_CONFIG: 日志系统配置
- SECURITY_CONFIG: 安全和限制配置
- ERROR_CONFIG: 错误处理配置
- TRADE_STRATEGY_CONFIG: 交易策略配置（预留）
- DATA_CONFIG: 数据存储配置

🔧 使用方法：
```python
# 获取配置
api_config = get_config('browser_api')
url = api_config['url']

# 更新配置
update_config('security', 'max_concurrent_browsers', 10)
```
"""

# ========== 比特浏览器API配置 ==========
BROWSER_API_CONFIG = {
    'url': 'http://127.0.0.1:54345',                    # 比特浏览器本地API服务地址
    'headers': {'Content-Type': 'application/json'},    # HTTP请求头，指定JSON格式
    'timeout': 10                                       # 请求超时时间（秒），防止请求卡死
}
"""
比特浏览器API配置说明：
- url: 比特浏览器启动后会在本地开启API服务，默认端口54345
- headers: 所有API请求都需要指定JSON格式的Content-Type
- timeout: 网络请求的超时时间，避免程序因网络问题卡死
"""

# ========== 默认交易参数配置 ==========
DEFAULT_TRADE_CONFIG = {
    'buy_price': '0.001',      # 默认买入价格（字符串格式，便于界面显示）
    'buy_quantity': '100',     # 默认买入数量（字符串格式）
    'run_count': '10',          # 默认运行次数（字符串格式，空字符串表示无限循环）
    'min_delay': '0.5',        # 默认最小延迟时间（秒），用于随机延迟的下限
    'max_delay': '2.0'         # 默认最大延迟时间（秒），用于随机延迟的上限
}
"""
默认交易参数说明：
- buy_price: 新建浏览器行时的默认买入价格
- buy_quantity: 新建浏览器行时的默认买入数量
- run_count: 新建浏览器行时的默认运行次数，1表示执行1次后停止
- min_delay/max_delay: 每次交易执行后的随机延迟范围，避免被检测为机器人

注意：所有参数都使用字符串格式，因为：
1. 便于直接设置到界面输入框
2. 避免数值类型转换问题
3. 支持空值表示特殊含义（如无限循环）
"""

# 币安交易配置
BINANCE_CONFIG = {
    'url': 'https://www.binance.com/zh-CN/markets/alpha-initials',
    'page_load_timeout': 30,   # 页面加载超时时间（秒）
    'element_wait_timeout': 10  # 元素等待超时时间（秒）
}

# 界面配置
UI_CONFIG = {
    'window_title': '比特浏览器自动化币安买卖脚本',
    'status_refresh_interval': 5000,  # 状态刷新间隔（毫秒）
    'column_widths': {
        'name': 150,           # 名称列宽度
        'buy_price': 80,       # 买入价格列宽度
        'buy_quantity': 80,    # 买入数量列宽度
        'status': 120,         # 状态列宽度
        'run_count': 80,       # 运行次数列宽度
        'set_run_count': 100,  # 设置运行次数列宽度
        'actions': 150         # 开关列宽度
    }
}

# 日志配置
LOG_CONFIG = {
    'enable_logging': False,
    'log_level': 'INFO',
    'log_file': 'automation.log',
    'max_log_size': 10 * 1024 * 1024,  # 10MB
    'backup_count': 5
}

# 安全配置
SECURITY_CONFIG = {
    'max_concurrent_browsers': 5,     # 最大并发浏览器数量（默认5个）
    'max_automation_threads': 20,     # 最大自动化线程数量
    'browser_startup_delay': 2,       # 浏览器启动间隔（秒）
    'automation_startup_delay': 1,    # 自动化启动间隔（秒）
    'enable_browser_limit': True,     # 是否启用浏览器数量限制
    'default_max_browsers': 5         # 默认最大浏览器数量
}

# 错误处理配置
ERROR_CONFIG = {
    'max_retry_count': 3,             # 最大重试次数
    'retry_delay': 5,                 # 重试延迟（秒）
    'auto_recovery': True,            # 是否自动恢复
    'error_notification': True        # 是否显示错误通知
}

# 交易策略配置（占位）
TRADE_STRATEGY_CONFIG = {
    'strategy_type': 'simple_buy',    # 策略类型
    'risk_management': {
        'max_loss_percent': 5,        # 最大亏损百分比
        'max_position_size': 1000,    # 最大仓位大小
        'stop_loss_enabled': False,   # 是否启用止损
        'take_profit_enabled': False  # 是否启用止盈
    }
}

# 数据存储配置
DATA_CONFIG = {
    'save_trade_history': False,       # 是否保存交易历史
    'history_file': 'trade_history.json',
    'save_browser_config': True,      # 是否保存浏览器配置
    'config_file': 'browser_config.json'
}

def get_config(config_name):
    """获取指定配置"""
    config_map = {
        'browser_api': BROWSER_API_CONFIG,
        'default_trade': DEFAULT_TRADE_CONFIG,
        'binance': BINANCE_CONFIG,
        'ui': UI_CONFIG,
        'log': LOG_CONFIG,
        'security': SECURITY_CONFIG,
        'error': ERROR_CONFIG,
        'trade_strategy': TRADE_STRATEGY_CONFIG,
        'data': DATA_CONFIG
    }
    return config_map.get(config_name, {})

def update_config(config_name, key, value):
    """更新配置值"""
    config_map = {
        'browser_api': BROWSER_API_CONFIG,
        'default_trade': DEFAULT_TRADE_CONFIG,
        'binance': BINANCE_CONFIG,
        'ui': UI_CONFIG,
        'log': LOG_CONFIG,
        'security': SECURITY_CONFIG,
        'error': ERROR_CONFIG,
        'trade_strategy': TRADE_STRATEGY_CONFIG,
        'data': DATA_CONFIG
    }
    
    config = config_map.get(config_name)
    if config and key in config:
        config[key] = value
        return True
    return False

# 验证配置的有效性
def validate_config():
    """验证配置的有效性"""
    errors = []
    
    # 验证延迟设置
    min_delay = float(DEFAULT_TRADE_CONFIG['min_delay'])
    max_delay = float(DEFAULT_TRADE_CONFIG['max_delay'])
    if min_delay > max_delay:
        errors.append("默认最小延迟不能大于最大延迟")
    
    # 验证数值范围
    if SECURITY_CONFIG['max_concurrent_browsers'] <= 0:
        errors.append("最大并发浏览器数量必须大于0")
    
    if UI_CONFIG['status_refresh_interval'] < 1000:
        errors.append("状态刷新间隔不能小于1秒")
    
    return errors

if __name__ == "__main__":
    # 测试配置
    print("配置验证结果:")
    errors = validate_config()
    if errors:
        for error in errors:
            print(f"❌ {error}")
    else:
        print("✅ 所有配置验证通过")
    
    print("\n当前配置:")
    print(f"API地址: {BROWSER_API_CONFIG['url']}")
    print(f"默认买入价格: {DEFAULT_TRADE_CONFIG['buy_price']}")
    print(f"默认买入数量: {DEFAULT_TRADE_CONFIG['buy_quantity']}")
    print(f"延迟范围: {DEFAULT_TRADE_CONFIG['min_delay']}-{DEFAULT_TRADE_CONFIG['max_delay']}秒")
