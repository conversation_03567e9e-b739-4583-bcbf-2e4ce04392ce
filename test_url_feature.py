"""
测试网址功能的脚本
"""
from browser_automation import BrowserAutomation
import time

def test_url_navigation():
    """测试网址导航功能"""
    print("=" * 60)
    print("测试网址导航功能")
    print("=" * 60)
    
    # 创建自动化实例
    automation = BrowserAutomation()
    
    # 获取第一个未启动的浏览器
    result = automation.get_browser_list(page=0)
    
    if result.get("success") and result["data"]["list"]:
        browser = result["data"]["list"][0]
        browser_id = browser['id']
        browser_name = browser['name']
        
        print(f"使用浏览器: {browser_name} (ID: {browser_id[:8]}...)")
        
        # 测试不同的网址
        test_urls = [
            "https://www.binance.com/zh-CN/markets/alpha-initials",
            "https://www.baidu.com",
            "https://www.google.com"
        ]
        
        for i, url in enumerate(test_urls, 1):
            print(f"\n{i}. 测试导航到: {url}")
            
            # 启动浏览器并导航到指定网址
            result = automation.open_browser(browser_id, url)
            
            if result.get("success"):
                print(f"✅ 浏览器启动成功并导航到: {url}")
                
                # 等待几秒钟让用户看到页面
                print("   等待5秒钟...")
                time.sleep(5)
                
                # 关闭浏览器
                automation.close_browser(browser_id)
                print("   浏览器已关闭")
                
                # 等待一下再测试下一个
                if i < len(test_urls):
                    print("   等待2秒钟再测试下一个...")
                    time.sleep(2)
            else:
                print(f"❌ 浏览器启动失败: {result.get('message', '未知错误')}")
                break
        
        print("\n✅ 网址导航功能测试完成!")
        print("💡 现在启动浏览器时会直接导航到指定网址")
        print("💡 开始自动化时不会重复导航，可以在页面上预先做设置")
        
    else:
        print("❌ 没有找到可用的浏览器窗口")
        print("   请在比特浏览器中创建一些窗口配置")

def test_automation_workflow():
    """测试完整的自动化工作流程"""
    print("\n" + "=" * 60)
    print("测试完整自动化工作流程")
    print("=" * 60)
    
    automation = BrowserAutomation()
    
    # 获取第一个未启动的浏览器
    result = automation.get_browser_list(page=0)
    
    if result.get("success") and result["data"]["list"]:
        browser = result["data"]["list"][0]
        browser_id = browser['id']
        browser_name = browser['name']
        
        print(f"使用浏览器: {browser_name}")
        
        # 1. 启动浏览器并导航到币安页面
        target_url = "https://www.binance.com/zh-CN/markets/alpha-initials"
        print(f"\n1. 启动浏览器并导航到: {target_url}")
        
        result = automation.open_browser(browser_id, target_url)
        if result.get("success"):
            print("✅ 浏览器启动成功，页面已加载")
            
            print("\n💡 此时用户可以在页面上进行以下操作:")
            print("   - 登录账户")
            print("   - 选择交易对")
            print("   - 设置交易参数")
            print("   - 进行其他页面设置")
            
            print("\n等待10秒钟模拟用户设置时间...")
            time.sleep(10)
            
            # 2. 开始自动化（不会重复导航）
            print("\n2. 开始自动化任务（不会重复导航到页面）")
            
            success = automation.start_automation(
                browser_id=browser_id,
                buy_price="0.001",
                buy_quantity="100", 
                run_count="2",
                min_delay="1",
                max_delay="3"
            )
            
            if success:
                print("✅ 自动化任务已启动")
                print("   - 不会重复导航到页面")
                print("   - 直接在当前页面执行交易逻辑")
                print("   - 保持用户的页面设置")
                
                # 等待自动化执行
                print("\n等待自动化执行...")
                time.sleep(15)
                
                # 停止自动化
                automation.stop_automation(browser_id)
                print("✅ 自动化任务已停止")
            else:
                print("❌ 自动化任务启动失败")
            
            # 3. 关闭浏览器
            print("\n3. 关闭浏览器")
            automation.close_browser(browser_id)
            print("✅ 浏览器已关闭")
            
        else:
            print(f"❌ 浏览器启动失败: {result.get('message', '未知错误')}")
    
    else:
        print("❌ 没有找到可用的浏览器窗口")

def main():
    """主函数"""
    try:
        print("🚀 开始测试新的网址功能...")
        
        # 测试网址导航
        test_url_navigation()
        
        # 测试完整工作流程
        test_automation_workflow()
        
        print("\n" + "=" * 60)
        print("🎉 所有测试完成!")
        print("=" * 60)
        print("✅ 新功能说明:")
        print("   1. 启动浏览器时会直接导航到指定网址")
        print("   2. 用户可以在页面上预先做设置")
        print("   3. 开始自动化时不会重复导航")
        print("   4. 保持用户的页面状态和设置")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
